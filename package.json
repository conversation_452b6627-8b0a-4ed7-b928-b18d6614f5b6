{"name": "watchlo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:cover": "jest --coverage", "test:watch": "jest --watch"}, "dependencies": {"@next/bundle-analyzer": "^15.0.3", "@vercel/analytics": "^1.3.1", "@vercel/speed-insights": "^1.0.12", "@vidstack/react": "^1.12.12", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "daisyui": "^4.12.13", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-react": "^8.3.0", "embla-carousel-wheel-gestures": "^8.0.1", "framer-motion": "^12.0.0-alpha.1", "hamburger-react": "^2.5.1", "hls.js": "^1.5.16", "html-react-parser": "^5.1.16", "lucide-react": "^0.441.0", "marked": "^14.1.2", "media-icons": "^1.1.5", "next": "15.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.2", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@types/jest": "^29.5.13", "@types/node": "^20", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "eslint": "^8", "eslint-config-next": "15.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prettier": "^2.8.8", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}, "overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}}