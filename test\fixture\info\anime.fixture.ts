export const animeInfoV2Fixture = {
  code: 200,
  message: "success",
  id: 1535,
  idMal: 1535,
  id_provider: {
    idGogo: "death-note",
    idGogoDub: "death-note-dub",
    idZoro: "death-note-60",
    id9anime: "death-note.2r4r",
    idPahe: "672",
  },
  title: {
    romaji: "DEATH NOTE",
    english: "Death Note",
    native: "DEATH NOTE",
    userPreferred: "DEATH NOTE",
  },
  dub: true,
  description:
    "<PERSON> Yagami is a genius high school student who is about to learn about life through a book of death. When a bored shinigami, a God of Death, named <PERSON><PERSON><PERSON> drops a black notepad called a <i>Death Note</i>, Light receives power over life and death with the stroke of a pen. Determined to use this dark gift for the best, <PERSON> sets out to rid the world of evil… namely, the people he believes to be evil. Should anyone hold such power?<br>\n<br>\nThe consequences of <PERSON>’s actions will set the world ablaze.<br>\n<br>\n(Source: Viz Media)",
  coverImage: {
    large:
      "https://s4.anilist.co/file/anilistcdn/media/anime/cover/medium/bx1535-4r88a1tsBEIz.jpg",
    medium:
      "https://s4.anilist.co/file/anilistcdn/media/anime/cover/small/bx1535-4r88a1tsBEIz.jpg",
    color: null,
  },
  bannerImage:
    "https://s4.anilist.co/file/anilistcdn/media/anime/banner/1535.jpg",
  genres: ["Mystery", "Psychological", "Supernatural", "Thriller"],
  tags: [
    {
      id: 648,
      name: "Crime",
    },
    {
      id: 694,
      name: "Detective",
    },
    {
      id: 104,
      name: "Anti-Hero",
    },
  ],
  status: "FINISHED",
  format: "TV",
  episodes: 37,
  year: 2006,
  season: "FALL",
  duration: 23,
  startIn: {
    year: 2006,
    month: 10,
    day: 4,
  },
  endIn: {
    year: 2007,
    month: 6,
    day: 27,
  },
  nextair: null,
  score: {
    averageScore: 84,
    decimalScore: 8.4,
  },
  popularity: 740433,
  siteUrl: "https://anilist.co/anime/1535",
  trailer: {
    id: "NlJZ-YgAt-c",
    site: "youtube",
    thumbnail: "https://i.ytimg.com/vi/NlJZ-YgAt-c/hqdefault.jpg",
  },
  studios: [
    {
      name: "MADHOUSE",
    },
  ],
  relation: [
    {
      id: 30021,
      idMal: 21,
      title: {
        romaji: "DEATH NOTE",
        english: "Death Note",
        native: "DEATH NOTE",
        userPreferred: "DEATH NOTE",
      },
      coverImage: {
        large:
          "https://s4.anilist.co/file/anilistcdn/media/manga/cover/medium/bx30021-FE6kmrfpuKyb.jpg",
        medium:
          "https://s4.anilist.co/file/anilistcdn/media/manga/cover/small/bx30021-FE6kmrfpuKyb.jpg",
        color: "#f1e4ae",
      },
      bannerImage:
        "https://s4.anilist.co/file/anilistcdn/media/manga/banner/n30021-eZbrTpIjv10E.jpg",
      genres: ["Drama", "Mystery", "Psychological", "Supernatural", "Thriller"],
      tags: [
        {
          id: 694,
          name: "Detective",
        },
      ],
      type: "MANGA",
      format: "MANGA",
      status: "FINISHED",
      episodes: null,
      duration: null,
      averageScore: 84,
      season: null,
    },
  ],
};

export const popularAnime = {
  id: 16498,
  idMal: 16498,
  status: "FINISHED",
  title: {
    userPreferred: "Shingeki no Kyojin",
    romaji: "Shingeki no Kyojin",
    english: "Attack on Titan",
    native: "進撃の巨人",
  },
  genres: ["Action", "Drama", "Fantasy", "Mystery"],
  tags: [
    {
      id: 82,
      name: "Male Protagonist",
    },
    {
      id: 1046,
      name: "Suicide",
    },
  ],
  trailer: {
    id: "luYOt2-c2TI",
    site: "youtube",
    thumbnail: "https://i.ytimg.com/vi/luYOt2-c2TI/hqdefault.jpg",
  },
  description:
    "Several hundred years ago, humans were nearly exterminated by titans. Titans are typically several stories tall, seem to have no intelligence, devour human beings and, worst of all, seem to do it for the pleasure rather than as a food source. A small percentage of humanity survived by walling themselves in a city protected by extremely high walls, even taller than the biggest of titans.<br><br>\r\nFlash forward to the present and the city has not seen a titan in over 100 years. Teenage boy Eren and his foster sister Mikasa witness something horrific as the city walls are destroyed by a colossal titan that appears out of thin air. As the smaller titans flood the city, the two kids watch in horror as their mother is eaten alive. Eren vows that he will murder every single titan and take revenge for all of mankind.<br><br>\r\n(Source: MangaHelpers) ",
  format: "TV",
  bannerImage:
    "https://s4.anilist.co/file/anilistcdn/media/anime/banner/16498-8jpFCOcDmneX.jpg",
  coverImage: {
    extraLarge:
      "https://s4.anilist.co/file/anilistcdn/media/anime/cover/large/bx16498-73IhOXpJZiMF.jpg",
    large:
      "https://s4.anilist.co/file/anilistcdn/media/anime/cover/medium/bx16498-73IhOXpJZiMF.jpg",
    medium:
      "https://s4.anilist.co/file/anilistcdn/media/anime/cover/small/bx16498-73IhOXpJZiMF.jpg",
    color: "#f1a143",
  },
  episodes: 25,
  meanScore: 84,
  duration: 24,
  season: "SPRING",
  seasonYear: 2013,
  averageScore: 84,
  nextAiringEpisode: null,
};

export const recentAnime = {
  id: "blue-lock",
  episodeId: "blue-lock-episode-nagi-additional-time-episode-1",
  episodeNumber: 1,
  title: "Blue Lock: Episode Nagi - Additional Time!",
  image: "https://gogocdn.net/cover/blue-lock-episode-nagi-additional-time.png",
  url: "https://anitaku.pe/blue-lock-episode-nagi-additional-time-episode-1",
};
